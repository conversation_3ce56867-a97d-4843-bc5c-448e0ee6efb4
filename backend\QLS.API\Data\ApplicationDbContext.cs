using Microsoft.EntityFrameworkCore;
using QLS.API.Models;

namespace QLS.API.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<Product> Products { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<Category> Categories { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Product-Category relationship
            modelBuilder.Entity<Product>()
                .HasOne(p => p.Category)
                .WithMany(c => c.Products)
                .HasForeignKey(p => p.CategoryId)
                .OnDelete(DeleteBehavior.SetNull);

            // Seed data
            modelBuilder.Entity<Category>().HasData(
                new Category { Id = 1, Name = "Văn học", Description = "Sách văn học trong và ngoài nước" },
                new Category { Id = 2, Name = "Khoa học", Description = "Sách khoa học và công nghệ" },
                new Category { Id = 3, Name = "Giáo dục", Description = "Sách giáo khoa và tham khảo" }
            );

            modelBuilder.Entity<Product>().HasData(
                new Product { Id = 1, Name = "Tắt đèn", Price = 50000, Description = "Tiểu thuyết của Ngô Tất Tố", CategoryId = 1, CreatedAt = DateTime.UtcNow },
                new Product { Id = 2, Name = "Lập trình C#", Price = 120000, Description = "Sách học lập trình C# cơ bản", CategoryId = 2, CreatedAt = DateTime.UtcNow }
            );

            modelBuilder.Entity<User>().HasData(
                new User 
                { 
                    Id = 1, 
                    Name = "Admin", 
                    Email = "<EMAIL>", 
                    Password = "admin123", 
                    Role = "Administrator",
                    CreatedAt = DateTime.UtcNow
                }
            );
        }
    }
}
