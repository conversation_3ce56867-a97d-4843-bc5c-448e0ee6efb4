<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><PERSON><PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON><PERSON> v<PERSON></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
        }
        .container {
            margin-top: 30px;
        }
        .card {
            box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
        }
        table {
            text-align: center;
        }
        .btn-edit {
            background-color: #ffc107;
            color: black;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="text-primary mb-0">📚 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> & <PERSON></h2>
        <div id="userInfo" class="d-none">
            <span class="me-3">Xin chào, <strong id="userName"></strong>!</span>
            <button class="btn btn-outline-danger btn-sm" onclick="logout()">Đăng xuất</button>
        </div>
        <div id="loginPrompt">
            <a href="login.html" class="btn btn-primary">Đăng nhập</a>
        </div>
    </div>

    <div class="row">
        <!-- Quản lý Sách -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">📖 Quản lý Sách</div>
                <div class="card-body">
                    <div class="mb-2">
                        <label for="bookId">ID Sách:</label>
                        <input type="text" id="bookId" class="form-control" readonly>
                    </div>
                    <input type="text" id="bookName" class="form-control mb-2" placeholder="Tên sách">
                    <input type="number" id="bookPrice" class="form-control mb-2" placeholder="Giá">
                    <input type="text" id="bookDescription" class="form-control mb-2" placeholder="Mô tả">
                    <select id="bookCategory" class="form-control mb-2">
                        <option value="">Chọn danh mục</option>
                    </select>
                    <button id="btnAddBook" class="btn btn-success w-100">Thêm / Cập Nhật</button>
                </div>
            </div>
        </div>

        <!-- Quản lý Người Dùng -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-success text-white">👤 Quản lý Người Dùng</div>
                <div class="card-body">
                    <div class="mb-2">
                        <label for="userId">ID Người Dùng:</label>
                        <input type="text" id="userId" class="form-control" readonly>
                    </div>
                    <input type="text" id="userName" class="form-control mb-2" placeholder="Tên người dùng">
                    <input type="email" id="userEmail" class="form-control mb-2" placeholder="Email">
                    <input type="password" id="userPassword" class="form-control mb-2" placeholder="Mật khẩu">
                    <input type="text" id="userRole" class="form-control mb-2" placeholder="Vai trò">
                    <input type="date" id="userDateOfBirth" class="form-control mb-2" placeholder="Ngày sinh">
                    <input type="text" id="userAddress" class="form-control mb-2" placeholder="Địa chỉ">
                    <input type="text" id="userPhoneNumber" class="form-control mb-2" placeholder="Số điện thoại">
                    <button id="btnAddUser" class="btn btn-success w-100">Thêm / Cập Nhật</button>
                </div>
            </div>
        </div>

        <!-- Quản lý Danh Mục -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">📂 Quản lý Danh Mục</div>
                <div class="card-body">
                    <div class="mb-2">
                        <label for="categoryId">ID Danh Mục:</label>
                        <input type="text" id="categoryId" class="form-control" readonly>
                    </div>
                    <input type="text" id="categoryName" class="form-control mb-2" placeholder="Tên danh mục">
                    <input type="text" id="categoryDescription" class="form-control mb-2" placeholder="Mô tả danh mục">
                    <button id="btnAddCategory" class="btn btn-success w-100">Thêm / Cập Nhật</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Danh sách dữ liệu -->
    <div class="mt-4">
        <!-- Bảng Sách -->
        <div class="card">
            <div class="card-header bg-primary text-white">📖 Danh sách Sách</div>
            <div class="card-body">
                <table class="table table-bordered">
                    <thead class="table-primary">
                        <tr>
                            <th>ID</th>
                            <th>Tên Sách</th>
                            <th>Giá</th>
                            <th>Mô Tả</th>
                            <th>Danh Mục</th>
                            <th>Hành Động</th>
                        </tr>
                    </thead>
                    <tbody id="bookList"></tbody>
                </table>
            </div>
        </div>

        <!-- Bảng Người Dùng -->
        <div class="card mt-3">
            <div class="card-header bg-success text-white">👤 Danh sách Người Dùng</div>
            <div class="card-body">
                <table class="table table-bordered">
                    <thead class="table-success">
                        <tr>
                            <th>ID</th>
                            <th>Tên</th>
                            <th>Email</th>
                            <th>Vai Trò</th>
                            <th>Ngày Sinh</th>
                            <th>Địa Chỉ</th>
                            <th>Số Điện Thoại</th>
                            <th>Ngày Tạo</th>
                            <th>Hành Động</th>
                        </tr>
                    </thead>
                    <tbody id="userList"></tbody>
                </table>
            </div>
        </div>

        <!-- Bảng Danh Mục -->
        <div class="card mt-3">
            <div class="card-header bg-warning text-dark">📂 Danh sách Danh Mục</div>
            <div class="card-body">
                <table class="table table-bordered">
                    <thead class="table-warning">
                        <tr>
                            <th>ID</th>
                            <th>Tên Danh Mục</th>
                            <th>Mô Tả</th>
                            <th>Hành Động</th>
                        </tr>
                    </thead>
                    <tbody id="categoryList"></tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- Main JS -->
<script src="main.js"></script>
</body>
</html>