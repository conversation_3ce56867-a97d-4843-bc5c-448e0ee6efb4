<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đ<PERSON>ng nhập - QLS System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #00d4aa;
            --primary-light: #26e7c7;
            --secondary-color: #ff6b35;
            --accent-color: #ffd23f;
            --success-color: #10b981;
            --error-color: #f87171;
            --warning-color: #fbbf24;
            --info-color: #3b82f6;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --text-muted: #d1d5db;
            --border-color: #e5e7eb;
            --bg-light: #f9fafb;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            --gradient-secondary: linear-gradient(135deg, var(--secondary-color), #ff8f65);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .login-container {
            width: 100%;
            max-width: 1000px;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 600px;
            animation: slideIn 0.8s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .login-left {
            background: var(--gradient-primary);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 3rem;
            position: relative;
            overflow: hidden;
        }

        .login-left::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .welcome-content {
            text-align: center;
            color: white;
            position: relative;
            z-index: 2;
        }

        .welcome-logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .welcome-logo i {
            font-size: 3rem;
            color: white;
        }

        .welcome-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .welcome-subtitle {
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            opacity: 0.9;
        }

        .welcome-description {
            font-size: 1rem;
            opacity: 0.8;
            line-height: 1.6;
            max-width: 300px;
        }

        .login-right {
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .login-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .login-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .input-container {
            position: relative;
        }

        .form-control {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            color: var(--text-primary);
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
            background: white;
        }

        .form-control:focus + .input-icon {
            color: var(--primary-color);
        }

        .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .password-toggle:hover {
            color: var(--primary-color);
        }

        /* reCAPTCHA Styles */
        .recaptcha-container {
            margin-bottom: 1.5rem;
        }

        .recaptcha-checkbox {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid #d1d5db;
            border-radius: 8px;
            padding: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
        }

        .recaptcha-input {
            display: none;
        }

        .recaptcha-label {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            cursor: pointer;
            margin: 0;
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .checkbox-mark {
            width: 20px;
            height: 20px;
            border: 2px solid #d1d5db;
            border-radius: 3px;
            position: relative;
            transition: all 0.3s ease;
            background: white;
        }

        .checkbox-mark::after {
            content: '';
            position: absolute;
            left: 5px;
            top: 1px;
            width: 5px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg) scale(0);
            transition: transform 0.2s ease;
        }

        .recaptcha-input:checked + .recaptcha-label .checkbox-mark {
            background: #4285f4;
            border-color: #4285f4;
        }

        .recaptcha-input:checked + .recaptcha-label .checkbox-mark::after {
            transform: rotate(45deg) scale(1);
        }

        .recaptcha-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .recaptcha-brand {
            text-align: right;
            font-size: 0.7rem;
            color: #6b7280;
        }

        .btn-login {
            width: 100%;
            padding: 1rem;
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 1rem;
        }

        .btn-login:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-login:disabled {
            background: #e5e7eb;
            cursor: not-allowed;
            transform: none;
        }

        .login-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--border-color);
        }

        .login-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .login-footer a:hover {
            text-decoration: underline;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .login-container {
                grid-template-columns: 1fr;
                max-width: 400px;
                min-height: auto;
            }
            
            .login-left {
                padding: 2rem;
                min-height: 200px;
            }
            
            .welcome-title {
                font-size: 1.8rem;
            }
            
            .login-right {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- Left Side - Welcome -->
        <div class="login-left">
            <div class="welcome-content">
                <div class="welcome-logo">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h1 class="welcome-title">Chào mừng trở lại!</h1>
                <p class="welcome-subtitle">Đăng nhập vào QLS System</p>
                <p class="welcome-description">Hệ thống quản lý sách hiện đại và thông minh</p>
            </div>
        </div>

        <!-- Right Side - Login Form -->
        <div class="login-right">
            <div class="login-header">
                <h2 class="login-title">Đăng nhập</h2>
                <p class="login-subtitle">Nhập thông tin để tiếp tục</p>
            </div>

            <!-- Alert Container -->
            <div id="alertContainer"></div>

            <!-- reCAPTCHA Checkbox -->
            <div class="recaptcha-container">
                <div class="recaptcha-checkbox">
                    <input type="checkbox" id="recaptchaCheck" class="recaptcha-input">
                    <label for="recaptchaCheck" class="recaptcha-label">
                        <div class="checkbox-mark"></div>
                        <span class="recaptcha-text">I'm not a robot</span>
                    </label>
                    <div class="recaptcha-logo">
                        <div class="recaptcha-brand">
                            <div>reCAPTCHA</div>
                            <div style="font-size: 0.6rem; color: #9ca3af;">Privacy - Terms</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Login Form -->
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username" class="form-label">Email / Tên đăng nhập</label>
                    <div class="input-container">
                        <i class="fas fa-user input-icon"></i>
                        <input type="text" class="form-control" id="username" required
                               placeholder="Nhập email hoặc tên đăng nhập">
                    </div>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">Mật khẩu</label>
                    <div class="input-container">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" class="form-control" id="password" required
                               placeholder="Nhập mật khẩu">
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                        </button>
                    </div>
                </div>

                <button type="submit" class="btn-login" id="loginBtn" disabled>
                    <span id="loginText">Tích chọn reCAPTCHA trước</span>
                </button>
            </form>

            <!-- Footer -->
            <div class="login-footer">
                <small class="text-muted">
                    Chưa có tài khoản?
                    <a href="index.html">Quay về trang chính</a>
                </small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const API_BASE = 'http://localhost:5184/api';
        let isRecaptchaChecked = false;

        function showAlert(message, type = 'danger') {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.innerHTML = message;
            alertContainer.innerHTML = '';
            alertContainer.appendChild(alertDiv);

            // Auto hide after 5 seconds
            setTimeout(() => {
                alertDiv.style.opacity = '0';
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.parentNode.removeChild(alertDiv);
                    }
                }, 300);
            }, 5000);
        }

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        function handleRecaptchaChange() {
            const recaptchaCheck = document.getElementById('recaptchaCheck');
            const loginBtn = document.getElementById('loginBtn');
            const loginText = document.getElementById('loginText');

            if (recaptchaCheck.checked) {
                isRecaptchaChecked = true;
                loginBtn.disabled = false;
                loginText.textContent = 'Đăng nhập ngay';
            } else {
                isRecaptchaChecked = false;
                loginBtn.disabled = true;
                loginText.textContent = 'Tích chọn reCAPTCHA trước';
            }
        }

        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (!isRecaptchaChecked) {
                showAlert('Vui lòng tích chọn "I\'m not a robot" trước!', 'warning');
                return;
            }

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showAlert('Vui lòng nhập đầy đủ thông tin!');
                return;
            }

            const loginBtn = document.getElementById('loginBtn');
            const loginText = document.getElementById('loginText');

            loginBtn.disabled = true;
            loginText.textContent = 'Đang xử lý...';

            try {
                const response = await fetch(`${API_BASE}/users/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    localStorage.setItem('qls_token', data.token);
                    localStorage.setItem('qls_user', JSON.stringify(data.user));

                    showAlert('Đăng nhập thành công! Đang chuyển hướng...', 'success');

                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                } else {
                    showAlert(data.message || 'Đăng nhập thất bại!');
                }
            } catch (error) {
                console.error('Login error:', error);
                showAlert('Lỗi kết nối! Vui lòng kiểm tra API server.');
            } finally {
                loginBtn.disabled = false;
                loginText.textContent = 'Đăng nhập ngay';
            }
        });

        document.addEventListener('DOMContentLoaded', function() {
            if (localStorage.getItem('qls_token')) {
                showAlert('Bạn đã đăng nhập! Đang chuyển hướng...', 'info');
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
                return;
            }

            const recaptchaCheck = document.getElementById('recaptchaCheck');
            recaptchaCheck.addEventListener('change', handleRecaptchaChange);

            // Add input focus effects
            const inputs = document.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                });
            });
        });
    </script>
</body>
</html>
