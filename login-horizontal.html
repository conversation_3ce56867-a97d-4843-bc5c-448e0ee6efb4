<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đ<PERSON>ng nhập - QLS System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #00d4aa;
            --primary-light: #26e7c7;
            --secondary-color: #ff6b35;
            --accent-color: #ffd23f;
            --success-color: #10b981;
            --error-color: #f87171;
            --warning-color: #fbbf24;
            --info-color: #3b82f6;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --text-muted: #d1d5db;
            --border-color: #e5e7eb;
            --bg-light: #f9fafb;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            --gradient-secondary: linear-gradient(135deg, var(--secondary-color), #ff8f65);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .login-container {
            width: 100%;
            max-width: 900px;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 25px;
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 700px;
            animation: slideIn 0.8s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .login-left {
            background: rgba(255, 255, 255, 0.95);
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .logo-container {
            width: 60px;
            height: 60px;
            margin: 0 auto 1rem;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            box-shadow: var(--shadow-md);
        }

        .login-title {
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.3rem;
        }

        .login-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 0.3rem;
        }

        .login-description {
            color: var(--text-light);
            font-size: 0.85rem;
        }

        .login-right {
            background: linear-gradient(135deg, #fff7ed 0%, #fef3c7 100%);
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .login-right::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 211, 61, 0.1), transparent);
            animation: rotate 10s linear infinite;
            z-index: 1;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .captcha-content {
            position: relative;
            z-index: 2;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .input-container {
            position: relative;
        }

        .form-control {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            color: var(--text-primary);
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
            background: white;
        }

        .form-control:focus + .input-icon {
            color: var(--primary-color);
        }

        .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .password-toggle:hover {
            color: var(--primary-color);
        }

        /* reCAPTCHA Styles */
        .recaptcha-container {
            margin-bottom: 1.5rem;
        }

        .recaptcha-checkbox {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid #d1d5db;
            border-radius: 8px;
            padding: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
        }

        .recaptcha-input {
            display: none;
        }

        .recaptcha-label {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            cursor: pointer;
            margin: 0;
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .checkbox-mark {
            width: 20px;
            height: 20px;
            border: 2px solid #d1d5db;
            border-radius: 3px;
            position: relative;
            transition: all 0.3s ease;
            background: white;
        }

        .checkbox-mark::after {
            content: '';
            position: absolute;
            left: 5px;
            top: 1px;
            width: 5px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg) scale(0);
            transition: transform 0.2s ease;
        }

        .recaptcha-input:checked + .recaptcha-label .checkbox-mark {
            background: #4285f4;
            border-color: #4285f4;
        }

        .recaptcha-input:checked + .recaptcha-label .checkbox-mark::after {
            transform: rotate(45deg) scale(1);
        }

        .recaptcha-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .recaptcha-brand {
            text-align: right;
            font-size: 0.7rem;
            color: #6b7280;
        }

        /* Robot Verification Styles */
        .robot-verification {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid var(--accent-color);
            border-radius: 18px;
            padding: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .robot-question {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .robot-emoji {
            font-size: 1.5rem;
            animation: bounce 2s ease-in-out infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .robot-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.8rem;
            margin-bottom: 1rem;
        }

        .robot-option {
            background: rgba(255, 255, 255, 0.8);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.85rem;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .robot-option:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .robot-option.selected {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border-color: var(--primary-color);
            transform: scale(1.05);
        }

        .robot-option.correct {
            background: linear-gradient(135deg, var(--success-color), #38d9a9);
            color: white;
            border-color: var(--success-color);
        }

        .robot-option.wrong {
            background: linear-gradient(135deg, var(--error-color), #ff8a8a);
            color: white;
            border-color: var(--error-color);
        }

        .robot-feedback {
            font-size: 0.9rem;
            font-weight: 600;
            margin-top: 1rem;
            padding: 0.5rem;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .robot-feedback.success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .robot-feedback.error {
            background: rgba(248, 113, 113, 0.1);
            color: var(--error-color);
        }

        .robot-refresh {
            background: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .robot-refresh:hover {
            background: #f9844a;
            transform: rotate(180deg);
        }

        .btn-login {
            width: 100%;
            padding: 1rem;
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 1rem;
        }

        .btn-login:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-login:disabled {
            background: #e5e7eb;
            cursor: not-allowed;
            transform: none;
        }

        .login-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--border-color);
        }

        .login-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .login-footer a:hover {
            text-decoration: underline;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .login-container {
                grid-template-columns: 1fr;
                max-width: 400px;
                min-height: auto;
            }
            
            .login-left {
                padding: 2rem;
                min-height: 200px;
            }
            
            .welcome-title {
                font-size: 1.8rem;
            }
            
            .login-right {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- Left Side - Login Form -->
        <div class="login-left">
            <div class="login-header">
                <div class="logo-container">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h1 class="login-title">Chào mừng trở lại!</h1>
                <p class="login-subtitle">Đăng nhập vào QLS System</p>
                <p class="login-description">Hệ thống quản lý sách hiện đại và thông minh</p>
            </div>

            <!-- Alert Container -->
            <div id="alertContainer"></div>

            <!-- reCAPTCHA Checkbox -->
            <div class="recaptcha-container">
                <div class="recaptcha-checkbox">
                    <input type="checkbox" id="recaptchaCheck" class="recaptcha-input">
                    <label for="recaptchaCheck" class="recaptcha-label">
                        <div class="checkbox-mark"></div>
                        <span class="recaptcha-text">I'm not a robot</span>
                    </label>
                    <div class="recaptcha-logo">
                        <div class="recaptcha-brand">
                            <div>reCAPTCHA</div>
                            <div style="font-size: 0.6rem; color: #9ca3af;">Privacy - Terms</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Login Form -->
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username" class="form-label">Email / Tên đăng nhập</label>
                    <div class="input-container">
                        <i class="fas fa-user input-icon"></i>
                        <input type="text" class="form-control" id="username" required
                               placeholder="Nhập email hoặc tên đăng nhập">
                    </div>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">Mật khẩu</label>
                    <div class="input-container">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" class="form-control" id="password" required
                               placeholder="Nhập mật khẩu">
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                        </button>
                    </div>
                </div>

                <button type="submit" class="btn-login" id="loginBtn" disabled>
                    <span id="loginText">Tích chọn reCAPTCHA trước</span>
                </button>
            </form>

            <!-- Footer -->
            <div class="login-footer">
                <small class="text-muted">
                    Chưa có tài khoản?
                    <a href="index.html">Quay về trang chính</a>
                </small>
            </div>
        </div>

        <!-- Right Side - CAPTCHA -->
        <div class="login-right">
            <div class="captcha-content">
                <!-- Robot Verification -->
                <div class="robot-verification" id="robotVerification" style="display: none;">
                    <div class="robot-question">
                        <span class="robot-emoji">🤖</span>
                        <span id="robotQuestionText">Con gì đi không có chân, bay không có cánh?</span>
                    </div>
                    <div class="robot-options" id="robotOptions">
                        <div class="robot-option" data-answer="Con rắn">Con rắn</div>
                        <div class="robot-option" data-answer="Con cá">Con cá</div>
                        <div class="robot-option" data-answer="Con số 8">Con số 8</div>
                        <div class="robot-option" data-answer="Con dao">Con dao</div>
                    </div>
                    <button type="button" class="robot-refresh" onclick="generateNewQuestion()">
                        <i class="fas fa-sync-alt"></i> Câu hỏi khác
                    </button>
                    <div class="robot-feedback" id="robotFeedback"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const API_BASE = 'http://localhost:5184/api';
        let isRecaptchaChecked = false;
        let isRobotVerified = false;
        let currentChallenge = null;

        const robotChallenges = [
            {
                question: 'Con gì đi không có chân, bay không có cánh?',
                options: ['Con rắn', 'Con cá', 'Con số 8', 'Con dao'],
                correct: 2,
                feedback: {
                    correct: '🎉 Chính xác! Con số 8 đi không có chân, bay không có cánh!',
                    wrong: '😅 Sai rồi! Thử lại nhé!'
                }
            },
            {
                question: 'Cái gì càng rửa càng bẩn?',
                options: ['Nước', 'Xà phòng', 'Khăn lau', 'Nước bẩn'],
                correct: 0,
                feedback: {
                    correct: '🎉 Đúng rồi! Nước càng rửa càng bẩn!',
                    wrong: '😅 Chưa đúng! Hãy suy nghĩ lại nhé!'
                }
            },
            {
                question: 'Ai là người không bao giờ nói dối?',
                options: ['Người câm', 'Người thật thà', 'Người ngủ', 'Người chết'],
                correct: 0,
                feedback: {
                    correct: '🎉 Thông minh! Người câm không bao giờ nói dối!',
                    wrong: '😅 Sai rồi! Thử lại xem!'
                }
            }
        ];

        function showAlert(message, type = 'danger') {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.innerHTML = message;
            alertContainer.innerHTML = '';
            alertContainer.appendChild(alertDiv);

            // Auto hide after 5 seconds
            setTimeout(() => {
                alertDiv.style.opacity = '0';
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.parentNode.removeChild(alertDiv);
                    }
                }, 300);
            }, 5000);
        }

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        function generateNewQuestion() {
            const challenge = robotChallenges[Math.floor(Math.random() * robotChallenges.length)];
            currentChallenge = challenge;

            const questionText = document.getElementById('robotQuestionText');
            const optionsContainer = document.getElementById('robotOptions');
            const feedback = document.getElementById('robotFeedback');

            questionText.textContent = challenge.question;
            feedback.textContent = '';
            feedback.className = 'robot-feedback';

            optionsContainer.innerHTML = challenge.options.map((option, index) => `
                <div class="robot-option" onclick="selectRobotOption(${index})">
                    ${option}
                </div>
            `).join('');
        }

        function selectRobotOption(selectedIndex) {
            const options = document.querySelectorAll('.robot-option');
            const feedback = document.getElementById('robotFeedback');
            const loginBtn = document.getElementById('loginBtn');
            const loginText = document.getElementById('loginText');

            // Clear previous selections
            options.forEach(option => {
                option.classList.remove('selected', 'correct', 'wrong');
            });

            // Mark selected option
            options[selectedIndex].classList.add('selected');

            setTimeout(() => {
                if (selectedIndex === currentChallenge.correct) {
                    // Correct answer
                    options[selectedIndex].classList.add('correct');
                    feedback.textContent = currentChallenge.feedback.correct;
                    feedback.className = 'robot-feedback success';

                    isRobotVerified = true;
                    loginBtn.disabled = false;
                    loginText.textContent = 'Đăng nhập ngay';

                } else {
                    // Wrong answer
                    options[selectedIndex].classList.add('wrong');
                    options[currentChallenge.correct].classList.add('correct');
                    feedback.textContent = currentChallenge.feedback.wrong;
                    feedback.className = 'robot-feedback error';

                    isRobotVerified = false;
                    loginBtn.disabled = true;
                    loginText.textContent = 'Hoàn thành xác thực trước';

                    // Generate new challenge after 2 seconds
                    setTimeout(() => {
                        generateNewQuestion();
                    }, 2000);
                }
            }, 500);
        }

        function handleRecaptchaChange() {
            const recaptchaCheck = document.getElementById('recaptchaCheck');
            const robotVerification = document.getElementById('robotVerification');
            const loginBtn = document.getElementById('loginBtn');
            const loginText = document.getElementById('loginText');

            if (recaptchaCheck.checked) {
                isRecaptchaChecked = true;
                robotVerification.style.display = 'block';
                generateNewQuestion();
                loginBtn.disabled = true;
                loginText.textContent = 'Hoàn thành xác thực trước';
            } else {
                isRecaptchaChecked = false;
                isRobotVerified = false;
                robotVerification.style.display = 'none';
                loginBtn.disabled = true;
                loginText.textContent = 'Tích chọn reCAPTCHA trước';
            }
        }

        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (!isRecaptchaChecked) {
                showAlert('Vui lòng tích chọn "I\'m not a robot" trước!', 'warning');
                return;
            }

            if (!isRobotVerified) {
                showAlert('Vui lòng hoàn thành xác thực chống robot trước!', 'warning');
                return;
            }

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showAlert('Vui lòng nhập đầy đủ thông tin!');
                return;
            }

            const loginBtn = document.getElementById('loginBtn');
            const loginText = document.getElementById('loginText');

            loginBtn.disabled = true;
            loginText.textContent = 'Đang xử lý...';

            try {
                const response = await fetch(`${API_BASE}/users/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    localStorage.setItem('qls_token', data.token);
                    localStorage.setItem('qls_user', JSON.stringify(data.user));

                    showAlert('Đăng nhập thành công! Đang chuyển hướng...', 'success');

                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                } else {
                    showAlert(data.message || 'Đăng nhập thất bại!');
                }
            } catch (error) {
                console.error('Login error:', error);
                showAlert('Lỗi kết nối! Vui lòng kiểm tra API server.');
            } finally {
                loginBtn.disabled = false;
                loginText.textContent = 'Đăng nhập ngay';
            }
        });

        document.addEventListener('DOMContentLoaded', function() {
            if (localStorage.getItem('qls_token')) {
                showAlert('Bạn đã đăng nhập! Đang chuyển hướng...', 'info');
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
                return;
            }

            const recaptchaCheck = document.getElementById('recaptchaCheck');
            recaptchaCheck.addEventListener('change', handleRecaptchaChange);

            // Add input focus effects
            const inputs = document.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                });
            });
        });
    </script>
</body>
</html>
