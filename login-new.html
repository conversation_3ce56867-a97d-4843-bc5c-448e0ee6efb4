<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QLS - <PERSON><PERSON>ng nhập</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #06d6a0;
            --primary-dark: #05b085;
            --secondary-color: #ffd166;
            --accent-color: #f72585;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2f8f5;
            --success-color: #38d9a9;
            --error-color: #ff6b6b;
            --warning-color: #ffd93d;
            --info-color: #4ecdc4;
            --shadow-sm: 0 2px 4px rgba(6, 214, 160, 0.1);
            --shadow-md: 0 8px 25px rgba(6, 214, 160, 0.15);
            --shadow-lg: 0 15px 35px rgba(6, 214, 160, 0.2);
            --gradient-primary: linear-gradient(135deg, #06d6a0 0%, #4ecdc4 50%, #45b7d1 100%);
            --gradient-secondary: linear-gradient(135deg, #ffd166 0%, #f9844a 100%);
            --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--gradient-bg);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(6, 214, 160, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 209, 102, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(78, 205, 196, 0.2) 0%, transparent 50%);
            z-index: -1;
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(10px) rotate(-1deg); }
        }

        .login-wrapper {
            width: 100%;
            max-width: 1200px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            min-height: 650px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-left {
            background: var(--gradient-primary);
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .login-left::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background:
                radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px),
                radial-gradient(circle, rgba(255,255,255,0.05) 1px, transparent 1px);
            background-size: 50px 50px, 25px 25px;
            animation: sparkle 15s linear infinite;
        }

        @keyframes sparkle {
            0% { transform: rotate(0deg) translateX(0); }
            100% { transform: rotate(360deg) translateX(20px); }
        }

        .login-left-content {
            position: relative;
            z-index: 2;
            animation: fadeInLeft 1s ease-out 0.3s both;
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .login-left h1 {
            font-size: 2.8rem;
            font-weight: 800;
            margin-bottom: 1rem;
            line-height: 1.2;
            text-shadow: 0 2px 10px rgba(0,0,0,0.1);
            background: linear-gradient(45deg, #ffffff, #f0f9ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .login-left p {
            font-size: 1.2rem;
            opacity: 0.95;
            margin-bottom: 2.5rem;
            line-height: 1.6;
            text-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .feature-list {
            list-style: none;
            text-align: left;
        }

        .feature-list li {
            display: flex;
            align-items: center;
            margin-bottom: 1.2rem;
            font-size: 1.05rem;
            opacity: 0;
            animation: fadeInUp 0.6s ease-out forwards;
        }

        .feature-list li:nth-child(1) { animation-delay: 0.8s; }
        .feature-list li:nth-child(2) { animation-delay: 1s; }
        .feature-list li:nth-child(3) { animation-delay: 1.2s; }
        .feature-list li:nth-child(4) { animation-delay: 1.4s; }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .feature-list i {
            margin-right: 0.75rem;
            font-size: 1.3rem;
            opacity: 0.9;
            background: var(--secondary-color);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
        }

        .login-right {
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            animation: fadeInRight 1s ease-out 0.5s both;
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-header h2 {
            font-size: 2.2rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 10px rgba(6, 214, 160, 0.2);
        }

        .login-header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
            font-weight: 500;
        }

        .demo-box {
            background: linear-gradient(135deg, #f0fdfa 0%, #ecfdf5 100%);
            border: 2px solid var(--border-color);
            border-radius: 16px;
            padding: 1.8rem;
            margin-bottom: 2rem;
            border-left: 5px solid var(--primary-color);
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .demo-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(6, 214, 160, 0.05) 50%, transparent 70%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .demo-box:hover::before {
            transform: translateX(100%);
        }

        .demo-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .demo-box h6 {
            color: var(--text-primary);
            font-weight: 700;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            position: relative;
            z-index: 1;
        }

        .demo-box h6 i {
            color: var(--primary-color);
            font-size: 1.1rem;
        }

        .demo-box p {
            margin: 0.4rem 0;
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-family: 'Courier New', monospace;
            font-weight: 600;
            position: relative;
            z-index: 1;
        }

        .form-group {
            margin-bottom: 1.8rem;
            position: relative;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.6rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-control {
            width: 100%;
            padding: 1rem 1.2rem;
            border: 2px solid var(--border-color);
            border-radius: 15px;
            font-size: 1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.9);
            color: var(--text-primary);
            backdrop-filter: blur(10px);
            position: relative;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(6, 214, 160, 0.15);
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 1);
        }

        .form-control::placeholder {
            color: #a0aec0;
            font-style: italic;
        }

        .form-group::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--gradient-primary);
            transition: width 0.3s ease;
        }

        .form-group:focus-within::after {
            width: 100%;
        }

        .btn-login {
            width: 100%;
            padding: 1rem 1.5rem;
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
            filter: brightness(1.1);
        }

        .btn-login:active:not(:disabled) {
            transform: translateY(-1px);
        }

        .btn-login:disabled {
            background: linear-gradient(135deg, #cbd5e0, #a0aec0);
            cursor: not-allowed;
            transform: none;
            filter: none;
        }

        .alert {
            padding: 1.2rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
            border: none;
            font-size: 0.9rem;
            font-weight: 600;
            position: relative;
            overflow: hidden;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .alert::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .alert-success {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #065f46;
            border: 1px solid rgba(6, 214, 160, 0.2);
        }

        .alert-success::before {
            background: var(--success-color);
        }

        .alert-danger {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #991b1b;
            border: 1px solid rgba(255, 107, 107, 0.2);
        }

        .alert-danger::before {
            background: var(--error-color);
        }

        .alert-info {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            color: #1e40af;
            border: 1px solid rgba(6, 214, 160, 0.2);
        }

        .alert-info::before {
            background: var(--info-color);
        }

        .login-footer {
            text-align: center;
            margin-top: 2.5rem;
            padding-top: 2rem;
            border-top: 2px solid var(--border-color);
            position: relative;
        }

        .login-footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 2px;
            background: var(--gradient-primary);
        }

        .login-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            position: relative;
            transition: all 0.3s ease;
        }

        .login-footer a::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--gradient-primary);
            transition: width 0.3s ease;
        }

        .login-footer a:hover::after {
            width: 100%;
        }

        .login-footer a:hover {
            transform: translateY(-1px);
        }

        .spinner-border-sm {
            width: 1.2rem;
            height: 1.2rem;
            border-width: 2px;
        }

        .captcha-container {
            background: #f8fafc;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .captcha-question {
            background: white;
            border: 2px solid var(--primary-color);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary-color);
            min-width: 120px;
            text-align: center;
            font-family: 'Courier New', monospace;
        }

        .captcha-input {
            width: 80px;
            padding: 0.75rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 1rem;
            text-align: center;
            font-weight: 600;
        }

        .captcha-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .captcha-refresh {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 1rem;
        }

        .captcha-refresh:hover {
            background: var(--primary-dark);
            transform: rotate(180deg);
        }

        .captcha-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .captcha-error {
            color: var(--error-color);
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }

        @media (max-width: 768px) {
            body::before {
                animation-duration: 15s;
            }

            .login-wrapper {
                grid-template-columns: 1fr;
                max-width: 420px;
                margin: 1rem;
            }

            .login-left {
                padding: 2.5rem 2rem;
                min-height: 250px;
            }

            .login-left h1 {
                font-size: 2rem;
            }

            .login-left p {
                font-size: 1rem;
                margin-bottom: 1.5rem;
            }

            .login-right {
                padding: 2.5rem 2rem;
            }

            .login-header h2 {
                font-size: 1.8rem;
            }

            .feature-list {
                display: none;
            }

            .demo-box {
                padding: 1.5rem;
            }

            .form-control {
                padding: 0.9rem 1rem;
            }

            .btn-login {
                padding: 0.9rem 1.5rem;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .login-wrapper {
                margin: 0.5rem;
                border-radius: 20px;
            }

            .login-left, .login-right {
                padding: 2rem 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-wrapper">
        <!-- Left Side - Branding -->
        <div class="login-left">
            <div class="login-left-content">
                <h1>📚 QLS System</h1>
                <p>Hệ thống quản lý sách, người dùng và danh mục hiện đại</p>
                
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> Quản lý sách thông minh</li>
                    <li><i class="fas fa-check"></i> Hệ thống người dùng an toàn</li>
                    <li><i class="fas fa-check"></i> Phân loại danh mục dễ dàng</li>
                    <li><i class="fas fa-check"></i> Giao diện thân thiện</li>
                </ul>
            </div>
        </div>

        <!-- Right Side - Login Form -->
        <div class="login-right">
            <div class="login-header">
                <h2>Đăng nhập</h2>
                <p>Chào mừng bạn quay trở lại!</p>
            </div>

            <div class="demo-box">
                <h6><i class="fas fa-info-circle"></i> Tài khoản demo</h6>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> admin123</p>
            </div>

            <div id="alertContainer"></div>

            <form id="loginForm">
                <div class="form-group">
                    <label for="username" class="form-label">Email / Tên đăng nhập</label>
                    <input type="text" class="form-control" id="username" required 
                           placeholder="Nhập email hoặc tên đăng nhập">
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">Mật khẩu</label>
                    <input type="password" class="form-control" id="password" required 
                           placeholder="Nhập mật khẩu">
                </div>

                <button type="submit" class="btn-login" id="loginBtn">
                    <span id="loginSpinner" class="spinner-border spinner-border-sm d-none"></span>
                    <span id="loginText">Đăng nhập</span>
                </button>
            </form>

            <div class="login-footer">
                <small class="text-muted">
                    Chưa có tài khoản? 
                    <a href="index.html">Quay về trang chính</a>
                </small>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const API_BASE = 'http://localhost:5184/api';

        function showAlert(message, type = 'danger') {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.innerHTML = message;
            alertContainer.innerHTML = '';
            alertContainer.appendChild(alertDiv);
        }

        function setLoading(loading) {
            const loginBtn = document.getElementById('loginBtn');
            const loginText = document.getElementById('loginText');
            const loginSpinner = document.getElementById('loginSpinner');

            if (loading) {
                loginBtn.disabled = true;
                loginText.textContent = 'Đang đăng nhập...';
                loginSpinner.classList.remove('d-none');
            } else {
                loginBtn.disabled = false;
                loginText.textContent = 'Đăng nhập';
                loginSpinner.classList.add('d-none');
            }
        }

        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showAlert('Vui lòng nhập đầy đủ thông tin!');
                return;
            }

            setLoading(true);

            try {
                const response = await fetch(`${API_BASE}/users/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    // Lưu token và thông tin user
                    localStorage.setItem('qls_token', data.token);
                    localStorage.setItem('qls_user', JSON.stringify(data.user));

                    showAlert('Đăng nhập thành công! Đang chuyển hướng...', 'success');

                    // Chuyển hướng sau 1.5 giây
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                } else {
                    showAlert(data.message || 'Đăng nhập thất bại!');
                }
            } catch (error) {
                console.error('Login error:', error);
                showAlert('Lỗi kết nối! Vui lòng kiểm tra API server.');
            } finally {
                setLoading(false);
            }
        });

        // Auto-fill demo credentials
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').value = '<EMAIL>';
            document.getElementById('password').value = 'admin123';
        });

        // Check if already logged in
        if (localStorage.getItem('qls_token')) {
            showAlert('Bạn đã đăng nhập! Đang chuyển hướng...', 'info');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
        }
    </script>
</body>
</html>
