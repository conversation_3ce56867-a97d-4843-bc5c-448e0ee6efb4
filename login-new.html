<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QLS - <PERSON><PERSON>ng nhập</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00d4aa;
            --primary-light: #26e7c7;
            --secondary-color: #ff6b9d;
            --accent-color: #ffd93d;
            --text-primary: #2a2d3a;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --bg-primary: #f0fdfa;
            --bg-secondary: #ffffff;
            --border-color: #d1fae5;
            --success-color: #10b981;
            --error-color: #f87171;
            --warning-color: #fbbf24;
            --shadow-sm: 0 1px 3px rgba(0, 212, 170, 0.12);
            --shadow-md: 0 4px 12px rgba(0, 212, 170, 0.15);
            --shadow-lg: 0 10px 25px rgba(0, 212, 170, 0.2);
            --shadow-xl: 0 20px 40px rgba(0, 212, 170, 0.25);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            position: relative;
            overflow: hidden;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            z-index: 1;
            pointer-events: none;
        }

        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            pointer-events: none;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: floatUp 20s infinite linear;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 60px;
            height: 60px;
            left: 20%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 100px;
            height: 100px;
            left: 70%;
            animation-delay: 4s;
        }

        .shape:nth-child(4) {
            width: 40px;
            height: 40px;
            left: 80%;
            animation-delay: 6s;
        }

        .shape:nth-child(5) {
            width: 120px;
            height: 120px;
            left: 50%;
            animation-delay: 8s;
        }

        @keyframes floatUp {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .login-container {
            position: relative;
            z-index: 10;
            width: 100%;
            max-width: 400px;
            animation: slideUp 1s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .login-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(25px);
            border-radius: 25px;
            padding: 2rem 1.8rem;
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
            border-radius: 30px 30px 0 0;
        }

        .login-card::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(0, 212, 170, 0.03), transparent);
            animation: rotate 20s linear infinite;
            z-index: -1;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
        }

        .logo-container {
            width: 60px;
            height: 60px;
            margin: 0 auto 1rem;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            box-shadow: var(--shadow-md);
            animation: pulse 3s ease-in-out infinite;
            position: relative;
            overflow: hidden;
        }

        .logo-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
            transform: rotate(45deg);
            animation: shine 3s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
            100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        }

        .login-title {
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.3rem;
            animation: fadeInDown 1s ease-out 0.5s both;
        }

        .login-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 0.3rem;
            animation: fadeInDown 1s ease-out 0.7s both;
        }

        .login-description {
            color: var(--text-light);
            font-size: 0.85rem;
            animation: fadeInDown 1s ease-out 0.9s both;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-form {
            animation: fadeInUp 1s ease-out 1.1s both;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.8rem;
            font-size: 0.95rem;
            position: relative;
        }

        .form-label::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 30px;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 1px;
        }

        .input-container {
            position: relative;
        }

        .form-control {
            width: 100%;
            padding: 1rem 1.2rem 1rem 3rem;
            border: 2px solid var(--border-color);
            border-radius: 18px;
            font-size: 0.95rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.8);
            color: var(--text-primary);
            backdrop-filter: blur(15px);
            font-weight: 500;
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            font-size: 1rem;
            transition: all 0.3s ease;
            z-index: 2;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(0, 212, 170, 0.15), 0 8px 25px rgba(0, 212, 170, 0.1);
            transform: translateY(-3px);
            background: rgba(255, 255, 255, 1);
        }

        .form-control:focus + .input-icon {
            color: var(--primary-color);
            transform: translateY(-50%) scale(1.1);
        }

        .form-control::placeholder {
            color: var(--text-light);
            font-weight: 400;
        }

        .password-toggle {
            position: absolute;
            right: 1.2rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            z-index: 2;
        }

        .password-toggle:hover {
            color: var(--primary-color);
            transform: translateY(-50%) scale(1.1);
        }

        /* reCAPTCHA Styles */
        .recaptcha-container {
            margin-bottom: 1.5rem;
        }

        .recaptcha-checkbox {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid #d1d5db;
            border-radius: 8px;
            padding: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .recaptcha-checkbox:hover {
            border-color: #9ca3af;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .recaptcha-input {
            display: none;
        }

        .recaptcha-label {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            cursor: pointer;
            margin: 0;
            font-size: 0.95rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .checkbox-mark {
            width: 24px;
            height: 24px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            position: relative;
            transition: all 0.3s ease;
            background: white;
        }

        .checkbox-mark::after {
            content: '';
            position: absolute;
            left: 6px;
            top: 2px;
            width: 6px;
            height: 12px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg) scale(0);
            transition: transform 0.2s ease;
        }

        .recaptcha-input:checked + .recaptcha-label .checkbox-mark {
            background: #4285f4;
            border-color: #4285f4;
        }

        .recaptcha-input:checked + .recaptcha-label .checkbox-mark::after {
            transform: rotate(45deg) scale(1);
        }

        .recaptcha-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .recaptcha-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .recaptcha-brand {
            text-align: right;
        }

        .recaptcha-brand > span {
            font-size: 0.75rem;
            font-weight: 600;
            color: #6b7280;
            display: block;
            line-height: 1;
        }

        .recaptcha-links {
            font-size: 0.65rem;
            color: #9ca3af;
            margin-top: 2px;
        }

        .recaptcha-links a {
            color: #9ca3af;
            text-decoration: none;
        }

        .recaptcha-links a:hover {
            color: #6b7280;
        }

        .recaptcha-checkbox.loading {
            pointer-events: none;
        }

        .recaptcha-checkbox.loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Robot Verification Styles */
        .robot-verification {
            background: linear-gradient(135deg, #fff7ed 0%, #fef3c7 100%);
            border: 2px solid var(--accent-color);
            border-radius: 18px;
            padding: 1.2rem;
            margin-bottom: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            animation: slideIn 0.5s ease-out;
        }

        .robot-verification::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 211, 61, 0.1), transparent);
            animation: rotate 10s linear infinite;
            z-index: 1;
        }

        .robot-verification-content {
            position: relative;
            z-index: 2;
        }

        .robot-question {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.8rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .robot-emoji {
            font-size: 1.5rem;
            animation: bounce 2s ease-in-out infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .robot-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.8rem;
            margin-bottom: 0.8rem;
        }

        .robot-option {
            background: rgba(255, 255, 255, 0.8);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.85rem;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .robot-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 212, 170, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .robot-option:hover::before {
            left: 100%;
        }

        .robot-option:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .robot-option.selected {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border-color: var(--primary-color);
            transform: scale(1.05);
        }

        .robot-option.correct {
            background: linear-gradient(135deg, var(--success-color), #38d9a9);
            color: white;
            border-color: var(--success-color);
            animation: correctPulse 0.6s ease-out;
        }

        .robot-option.wrong {
            background: linear-gradient(135deg, var(--error-color), #ff8a8a);
            color: white;
            border-color: var(--error-color);
            animation: wrongShake 0.6s ease-out;
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1.05); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .robot-feedback {
            font-size: 0.9rem;
            font-weight: 600;
            margin-top: 1rem;
            padding: 0.5rem;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .robot-feedback.success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .robot-feedback.error {
            background: rgba(248, 113, 113, 0.1);
            color: var(--error-color);
        }

        .robot-refresh {
            background: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .robot-refresh:hover {
            background: #f9844a;
            transform: rotate(180deg);
        }

        .robot-verified {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border-color: var(--success-color);
        }

        .robot-verified .robot-question {
            color: var(--success-color);
        }

        .btn-login {
            width: 100%;
            padding: 1.1rem 1.5rem;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            color: white;
            border: none;
            border-radius: 18px;
            font-size: 1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.6rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
            margin-top: 0.8rem;
            box-shadow: var(--shadow-md);
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:hover:not(:disabled) {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
            background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-color) 100%);
        }

        .btn-login:active:not(:disabled) {
            transform: translateY(-2px);
        }

        .btn-login:disabled {
            background: linear-gradient(135deg, #e5e7eb, #d1d5db);
            cursor: not-allowed;
            transform: none;
            box-shadow: var(--shadow-sm);
        }

        .btn-login .btn-text {
            transition: all 0.3s ease;
        }

        .btn-login:hover .btn-text {
            transform: scale(1.05);
        }

        .alert {
            padding: 1.2rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
            border: none;
            font-size: 0.9rem;
            font-weight: 600;
            position: relative;
            overflow: hidden;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .alert::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .alert-success {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #065f46;
            border: 1px solid rgba(6, 214, 160, 0.2);
        }

        .alert-success::before {
            background: var(--success-color);
        }

        .alert-danger {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #991b1b;
            border: 1px solid rgba(255, 107, 107, 0.2);
        }

        .alert-danger::before {
            background: var(--error-color);
        }

        .alert-info {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            color: #1e40af;
            border: 1px solid rgba(6, 214, 160, 0.2);
        }

        .alert-info::before {
            background: var(--info-color);
        }

        .alert-warning {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border: 1px solid rgba(251, 191, 36, 0.2);
        }

        .alert-warning::before {
            background: var(--warning-color);
        }

        .login-footer {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 2px solid var(--border-color);
            position: relative;
        }

        .login-footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 2px;
            background: var(--gradient-primary);
        }

        .login-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            position: relative;
            transition: all 0.3s ease;
        }

        .login-footer a::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--gradient-primary);
            transition: width 0.3s ease;
        }

        .login-footer a:hover::after {
            width: 100%;
        }

        .login-footer a:hover {
            transform: translateY(-1px);
        }

        .spinner-border-sm {
            width: 1.2rem;
            height: 1.2rem;
            border-width: 2px;
        }

        .captcha-container {
            background: #f8fafc;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .captcha-question {
            background: white;
            border: 2px solid var(--primary-color);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary-color);
            min-width: 120px;
            text-align: center;
            font-family: 'Courier New', monospace;
        }

        .captcha-input {
            width: 80px;
            padding: 0.75rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 1rem;
            text-align: center;
            font-weight: 600;
        }

        .captcha-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .captcha-refresh {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 1rem;
        }

        .captcha-refresh:hover {
            background: var(--primary-dark);
            transform: rotate(180deg);
        }

        .captcha-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .captcha-error {
            color: var(--error-color);
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }

        .login-footer {
            text-align: center;
            margin-top: 2.5rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
            animation: fadeIn 1s ease-out 1.5s both;
        }

        .login-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            position: relative;
            transition: all 0.3s ease;
        }

        .login-footer a::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transition: width 0.3s ease;
        }

        .login-footer a:hover::after {
            width: 100%;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @media (max-width: 768px) {
            .login-container {
                max-width: 400px;
                margin: 1rem;
            }

            .login-card {
                padding: 2.5rem 2rem;
                border-radius: 25px;
            }

            .login-title {
                font-size: 2rem;
            }

            .logo-container {
                width: 70px;
                height: 70px;
                font-size: 1.8rem;
            }

            .form-control {
                padding: 1rem 1.2rem 1rem 3rem;
            }

            .btn-login {
                padding: 1.1rem 1.5rem;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 0.5rem;
            }

            .login-card {
                padding: 2rem 1.5rem;
                border-radius: 20px;
            }

            .login-title {
                font-size: 1.8rem;
            }

            .floating-shapes {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Floating Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <!-- Login Container -->
    <div class="login-container">
        <div class="login-card">
            <!-- Header -->
            <div class="login-header">
                <div class="logo-container">
                    <i class="fas fa-book-open"></i>
                </div>
                <h1 class="login-title">Chào mừng trở lại!</h1>
                <p class="login-subtitle">Đăng nhập vào QLS System</p>
                <p class="login-description">Hệ thống quản lý sách hiện đại và thông minh</p>
            </div>

            <!-- Alert Container -->
            <div id="alertContainer"></div>

            <!-- reCAPTCHA Checkbox -->
            <div class="recaptcha-container">
                <div class="recaptcha-checkbox">
                    <input type="checkbox" id="recaptchaCheck" class="recaptcha-input">
                    <label for="recaptchaCheck" class="recaptcha-label">
                        <div class="checkbox-mark"></div>
                        <span class="recaptcha-text">I'm not a robot</span>
                    </label>
                    <div class="recaptcha-logo">
                        <div class="recaptcha-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="#4285f4"/>
                                <circle cx="12" cy="12" r="8" stroke="#34a853" stroke-width="2" fill="none"/>
                            </svg>
                        </div>
                        <div class="recaptcha-brand">
                            <span>reCAPTCHA</span>
                            <div class="recaptcha-links">
                                <a href="#" onclick="return false;">Privacy</a> - <a href="#" onclick="return false;">Terms</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Robot Verification -->
            <div id="robotVerification" class="robot-verification" style="display: none;">
                <div class="robot-verification-content">
                    <div class="robot-question">
                        <span class="robot-emoji">🤖</span>
                        <span id="robotQuestionText">Chứng minh bạn không phải robot!</span>
                    </div>
                    <div id="robotChallenge"></div>
                    <div id="robotFeedback" class="robot-feedback"></div>
                    <button type="button" class="robot-refresh" onclick="generateRobotChallenge()">
                        <i class="fas fa-sync-alt"></i> Câu hỏi khác
                    </button>
                </div>
            </div>

            <!-- Login Form -->
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username" class="form-label">Email / Tên đăng nhập</label>
                    <div class="input-container">
                        <i class="fas fa-user input-icon"></i>
                        <input type="text" class="form-control" id="username" required
                               placeholder="Nhập email hoặc tên đăng nhập của bạn">
                    </div>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">Mật khẩu</label>
                    <div class="input-container">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" class="form-control" id="password" required
                               placeholder="Nhập mật khẩu của bạn">
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                        </button>
                    </div>
                </div>

                <button type="submit" class="btn-login" id="loginBtn" disabled>
                    <span id="loginSpinner" class="spinner-border spinner-border-sm d-none"></span>
                    <span class="btn-text" id="loginText">Hoàn thành xác thực trước</span>
                    <i class="fas fa-shield-alt"></i>
                </button>
            </form>

            <!-- Footer -->
            <div class="login-footer">
                <small class="text-muted">
                    Chưa có tài khoản?
                    <a href="index.html">Quay về trang chính</a>
                </small>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const API_BASE = 'http://localhost:5184/api';

        function showAlert(message, type = 'danger') {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.innerHTML = message;
            alertContainer.innerHTML = '';
            alertContainer.appendChild(alertDiv);

            // Auto hide after 5 seconds
            setTimeout(() => {
                alertDiv.style.opacity = '0';
                alertDiv.style.transform = 'translateX(-20px)';
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.parentNode.removeChild(alertDiv);
                    }
                }, 300);
            }, 5000);
        }

        function setLoading(loading) {
            const loginBtn = document.getElementById('loginBtn');
            const loginText = document.getElementById('loginText');
            const loginSpinner = document.getElementById('loginSpinner');

            if (loading) {
                loginBtn.disabled = true;
                loginText.textContent = 'Đang xử lý...';
                loginSpinner.classList.remove('d-none');
            } else {
                loginBtn.disabled = false;
                loginText.textContent = 'Đăng nhập ngay';
                loginSpinner.classList.add('d-none');
            }
        }

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        // Robot Verification System
        let isRecaptchaChecked = false;
        let isRobotVerified = false;
        let currentChallenge = null;

        const robotChallenges = [
            {
                type: 'funny_question',
                question: 'Con gì đi không có chân, bay không có cánh?',
                options: ['Con rắn', 'Con cá', 'Con số 8', 'Con dao'],
                correct: 2,
                feedback: {
                    correct: '🎉 Chính xác! Con số 8 đi không có chân, bay không có cánh!',
                    wrong: '😅 Sai rồi! Thử lại nhé!'
                }
            },
            {
                type: 'funny_question',
                question: 'Cái gì càng rửa càng bẩn?',
                options: ['Nước', 'Xà phòng', 'Khăn lau', 'Nước bẩn'],
                correct: 0,
                feedback: {
                    correct: '🎉 Đúng rồi! Nước càng rửa càng bẩn!',
                    wrong: '😅 Chưa đúng! Hãy suy nghĩ lại nhé!'
                }
            },
            {
                type: 'funny_question',
                question: 'Ai là người không bao giờ nói dối?',
                options: ['Người câm', 'Người thật thà', 'Người ngủ', 'Người chết'],
                correct: 0,
                feedback: {
                    correct: '🎉 Thông minh! Người câm không bao giờ nói dối!',
                    wrong: '😅 Sai rồi! Thử lại xem!'
                }
            },
            {
                type: 'math_fun',
                question: 'Nếu 1 con gà đẻ 1 quả trứng trong 1 ngày, thì 5 con gà đẻ bao nhiêu quả trong 5 ngày?',
                options: ['5 quả', '25 quả', '1 quả', '10 quả'],
                correct: 1,
                feedback: {
                    correct: '🎉 Chính xác! 5 con gà x 5 ngày = 25 quả trứng!',
                    wrong: '😅 Tính lại nhé! 5 con gà trong 5 ngày...'
                }
            },
            {
                type: 'logic_fun',
                question: 'Trong một cuộc đua, bạn vượt qua người thứ 2. Bây giờ bạn đang ở vị trí nào?',
                options: ['Thứ 1', 'Thứ 2', 'Thứ 3', 'Cuối bảng'],
                correct: 1,
                feedback: {
                    correct: '🎉 Đúng! Vượt qua người thứ 2 thì bạn ở vị trí thứ 2!',
                    wrong: '😅 Suy nghĩ lại nhé! Vượt qua người thứ 2...'
                }
            },
            {
                type: 'emoji_fun',
                question: 'Emoji nào mô tả đúng nhất trạng thái của bạn khi quên mật khẩu?',
                options: ['😭', '😂', '😴', '🤔'],
                correct: 0,
                feedback: {
                    correct: '🎉 Đúng rồi! 😭 là cảm giác khi quên mật khẩu!',
                    wrong: '😅 Không đúng lắm! Hãy thử lại!'
                }
            },
            {
                type: 'tech_fun',
                question: 'Điều gì xảy ra khi bạn nhấn Ctrl+Z trong đời thực?',
                options: ['Hoàn tác hành động', 'Không có gì', 'Máy tính bị lag', 'Thời gian lùi lại'],
                correct: 1,
                feedback: {
                    correct: '🎉 Chính xác! Trong đời thực không có Ctrl+Z!',
                    wrong: '😅 Ước gì có Ctrl+Z trong đời thực nhỉ!'
                }
            }
        ];

        function generateRobotChallenge() {
            const challenge = robotChallenges[Math.floor(Math.random() * robotChallenges.length)];
            currentChallenge = challenge;

            const challengeContainer = document.getElementById('robotChallenge');
            const questionText = document.getElementById('robotQuestionText');
            const feedback = document.getElementById('robotFeedback');

            questionText.textContent = challenge.question;
            feedback.textContent = '';
            feedback.className = 'robot-feedback';

            challengeContainer.innerHTML = `
                <div class="robot-options">
                    ${challenge.options.map((option, index) => `
                        <div class="robot-option" onclick="selectRobotOption(${index})">
                            ${option}
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function selectRobotOption(selectedIndex) {
            const options = document.querySelectorAll('.robot-option');
            const feedback = document.getElementById('robotFeedback');
            const loginBtn = document.getElementById('loginBtn');
            const loginText = document.getElementById('loginText');
            const robotVerification = document.getElementById('robotVerification');

            // Clear previous selections
            options.forEach(option => {
                option.classList.remove('selected', 'correct', 'wrong');
            });

            // Mark selected option
            options[selectedIndex].classList.add('selected');

            setTimeout(() => {
                if (selectedIndex === currentChallenge.correct) {
                    // Correct answer
                    options[selectedIndex].classList.add('correct');
                    feedback.textContent = currentChallenge.feedback.correct;
                    feedback.className = 'robot-feedback success';

                    isRobotVerified = true;
                    loginBtn.disabled = false;
                    loginText.textContent = 'Đăng nhập ngay';

                    // Change verification box to success state
                    robotVerification.classList.add('robot-verified');

                    setTimeout(() => {
                        robotVerification.style.opacity = '0.7';
                        robotVerification.style.transform = 'scale(0.95)';
                    }, 1000);

                } else {
                    // Wrong answer
                    options[selectedIndex].classList.add('wrong');
                    options[currentChallenge.correct].classList.add('correct');
                    feedback.textContent = currentChallenge.feedback.wrong;
                    feedback.className = 'robot-feedback error';

                    isRobotVerified = false;
                    loginBtn.disabled = true;
                    loginText.textContent = 'Hoàn thành xác thực trước';

                    // Generate new challenge after 2 seconds
                    setTimeout(() => {
                        generateRobotChallenge();
                    }, 2000);
                }
            }, 500);
        }

        // reCAPTCHA handling
        function handleRecaptchaChange() {
            const recaptchaCheck = document.getElementById('recaptchaCheck');
            const recaptchaContainer = document.querySelector('.recaptcha-checkbox');
            const robotVerification = document.getElementById('robotVerification');
            const loginBtn = document.getElementById('loginBtn');
            const loginText = document.getElementById('loginText');

            if (recaptchaCheck.checked) {
                // Show loading state
                recaptchaContainer.classList.add('loading');

                setTimeout(() => {
                    recaptchaContainer.classList.remove('loading');
                    isRecaptchaChecked = true;

                    // Show robot verification challenge
                    robotVerification.style.display = 'block';
                    generateRobotChallenge();

                    // Update button state
                    loginBtn.disabled = true;
                    loginText.textContent = 'Hoàn thành xác thực trước';
                }, 1500);
            } else {
                isRecaptchaChecked = false;
                isRobotVerified = false;
                robotVerification.style.display = 'none';
                loginBtn.disabled = true;
                loginText.textContent = 'Tích chọn reCAPTCHA trước';
            }
        }

        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            // Check reCAPTCHA first
            if (!isRecaptchaChecked) {
                showAlert('Vui lòng tích chọn "I\'m not a robot" trước!', 'warning');
                return;
            }

            // Check robot verification
            if (!isRobotVerified) {
                showAlert('Vui lòng hoàn thành xác thực chống robot trước!', 'warning');
                return;
            }

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showAlert('Vui lòng nhập đầy đủ thông tin!');
                return;
            }

            setLoading(true);

            try {
                const response = await fetch(`${API_BASE}/users/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    // Lưu token và thông tin user
                    localStorage.setItem('qls_token', data.token);
                    localStorage.setItem('qls_user', JSON.stringify(data.user));

                    showAlert('Đăng nhập thành công! Đang chuyển hướng...', 'success');

                    // Chuyển hướng sau 1.5 giây
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                } else {
                    showAlert(data.message || 'Đăng nhập thất bại!');
                }
            } catch (error) {
                console.error('Login error:', error);
                showAlert('Lỗi kết nối! Vui lòng kiểm tra API server.');
            } finally {
                setLoading(false);
            }
        });

        // Check if already logged in and initialize
        document.addEventListener('DOMContentLoaded', function() {
            if (localStorage.getItem('qls_token')) {
                showAlert('Bạn đã đăng nhập! Đang chuyển hướng...', 'info');
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
                return;
            }

            // Initialize robot verification
            generateRobotChallenge();

            // Add input focus effects
            const inputs = document.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                });
            });
        });
    </script>
</body>
</html>
